using Localization.Resources.AbpUi;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Localization;
using EasyAbp.ProcessManagement;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC;


[ExcludeFromCodeCoverage]
[DependsOn(
    typeof(ProcessManagementHttpApiModule),
    typeof(SISPRECApplicationContractsModule),
    typeof(SISPRECProcessaPrecatorioApplicationModule),
    typeof(AbpSettingManagementHttpApiModule),
    typeof(SISPRECSincronizacaoLegadoModule)
    )]
public class SISPRECHttpApiModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        ConfigureLocalization();
    }

    private void ConfigureLocalization()
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            _ = options.Resources
                .Get<SISPRECResource>()
                .AddBaseTypes(
                    typeof(AbpUiResource)
                );
        });
    }
}
