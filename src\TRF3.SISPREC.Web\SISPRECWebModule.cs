using Autofac;
using ConsultaCNPJSOAP;
using ConsultaCPFSOAP;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.OpenApi.Models;
using Quartz;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using TRF3.SISPREC.Apoio;
using TRF3.SISPREC.ControleProcessamentos;
using TRF3.SISPREC.Controles.Dtos;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Localization;
using TRF3.SISPREC.MultiTenancy;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.ProcessaPrecatorio.ConfiguracaoServicos;
using TRF3.SISPREC.ProcessaPrecatorio.Workers;
using TRF3.SISPREC.UfepContexts;
using TRF3.SISPREC.VerificacoesCnpjCpf;
using TRF3.SISPREC.Web.Themes.LeptonXLite.Bundling;
using TRF3.SISPREC.Web.Menus;
using TRF3.SISPREC.Web.Toolbars;
using EasyAbp.ProcessManagement.Web;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Pages.Shared.Components.AbpApplicationPath;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Auditing;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.BackgroundJobs.Quartz;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.BackgroundWorkers.Quartz;
using Volo.Abp.Modularity;
using Volo.Abp.Quartz;
using Volo.Abp.Security.Claims;
using Volo.Abp.SettingManagement.Web;
using Volo.Abp.Swashbuckle;
using Volo.Abp.Ui.LayoutHooks;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using TRF3.SISPREC.Web.Bundling;

namespace TRF3.SISPREC.Web;

[DependsOn(
    typeof(ProcessManagementWebModule),
    typeof(SISPRECHttpApiModule),
    typeof(SISPRECApplicationModule),
    typeof(SISPRECEntityFrameworkCoreModule),
    typeof(SISPRECSincDominioApplicationModule),
    typeof(AbpAutofacModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpSettingManagementWebModule),
    typeof(AbpBackgroundJobsQuartzModule),
    typeof(AbpBackgroundWorkersQuartzModule)
    )]
[ExcludeFromCodeCoverage]
public class SISPRECWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        IWebHostEnvironment hostingEnvironment = context.Services.GetHostingEnvironment();
        IConfiguration configuration = context.Services.GetConfiguration();

        if (hostingEnvironment.IsDevelopment() || hostingEnvironment.IsStaging())
            AplicarMigrations(context);

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(SISPRECResource),
                typeof(SISPRECDomainModule).Assembly,
                typeof(SISPRECDomainSharedModule).Assembly,
                typeof(SISPRECApplicationModule).Assembly,
                typeof(SISPRECApplicationContractsModule).Assembly,
                typeof(SISPRECWebModule).Assembly
            );
        });

        PreConfigure<AbpQuartzOptions>(options =>
        {
            options.Configurator = configure =>
            {
                configure.InterruptJobsOnShutdownWithWait = true;
                configure.InterruptJobsOnShutdown = true;

                configure.UsePersistentStore(storeOptions =>
                {
                    storeOptions.UseProperties = true;
                    storeOptions.UseNewtonsoftJsonSerializer();
                    storeOptions.UseSqlServer(configuration.GetConnectionString("Default")!);
                    storeOptions.UseClustering(c =>
                    {
                        c.CheckinMisfireThreshold = TimeSpan.FromMinutes(2);
                        c.CheckinInterval = TimeSpan.FromMinutes(2);
                    });
                });
            };
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        IWebHostEnvironment hostingEnvironment = context.Services.GetHostingEnvironment();
        IConfiguration configuration = context.Services.GetConfiguration();

        context.Services.AddModeloConfiguration();
        ConfigureServicesLegado(configuration, context.Services);
        ConfigureAuthentication(context);
        ConfigureUrls(configuration);
        ConfigureBundles();
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        ConfigureSwaggerServices(context.Services);
        ConfigureLayoutHooks();
        ConfigureAuditingOptions();
        ConfigureBackgroundJobQuartzOptions();
        RegistrarPermissoes(context);
        ConfigureKestrelServerOptions(context);
        ConfigureAbpBackgroundJobOptions(configuration, context);
        context.Services.AddSameSiteCookiePolicy(); // cookie policy to deal with temporary browser incompatibilities
        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.TokenCookie.Expiration = TimeSpan.FromHours(12);
            options.TokenCookie.MaxAge = TimeSpan.FromHours(12);
            options.TokenCookie.HttpOnly = false;
            options.TokenCookie.IsEssential = false;
            options.TokenCookie.SameSite = SameSiteMode.Strict;
            options.TokenCookie.SecurePolicy = CookieSecurePolicy.Always;
            options.TokenCookie.Path = SisprecUrlHelper.GetPathBase(configuration, true, false);
        });
    }

    private void ConfigureAbpBackgroundJobOptions(IConfiguration configuration, ServiceConfigurationContext context)
    {
        bool abpBackgroundJobEnabled = ESimNaoHelper.ParseToBool(configuration["AppSettings:AbpBackgroundJobHabilitado"]);

        Configure((Action<AbpBackgroundJobOptions>)(options => { options.IsJobExecutionEnabled = abpBackgroundJobEnabled; }));
        context.Services.RemoveAll(typeof(IBackgroundJobManager));
        context.Services.AddTransient<IBackgroundJobManager, SISPRECQuartzBackgroundJobManager>();
    }

    private static void ConfigureKestrelServerOptions(ServiceConfigurationContext context)
    {
        context.Services.Configure<KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestBodySize = 1L * 1024L * 1024L * 1024L; // 1 GB
        });
    }

    private static void RegistrarPermissoes(ServiceConfigurationContext context)
    {
        context.Services.AddAuthorization(options =>
        {
            SISPRECPermissoes.RegistrarPermissoes(options);
        });
    }

    /// <summary>
    /// Config de tratamento de exceções durante execução de background jobs
    /// </summary>
    private void ConfigureBackgroundJobQuartzOptions()
    {
        Configure<AbpBackgroundJobQuartzOptions>(options =>
        {
            options.RetryCount = 3;
            options.RetryIntervalMillisecond = 3 * 60 * 1000;
        });
    }

    private void ConfigureAuditingOptions()
    {
        Configure<AbpAuditingOptions>(options =>
        {
            options.IsEnabled = true;
            options.IsEnabledForAnonymousUsers = false;
            options.IsEnabledForIntegrationServices = false;
            options.IsEnabledForGetRequests = false;
        });
    }

    /// <summary>
    /// Config para componentes do Projeto Web carregarem resources considerando o pathBase (arquivos .js, .css, imgs, etc)
    /// </summary>
    private void ConfigureLayoutHooks()
    {
        Configure<AbpLayoutHookOptions>(options =>
        {
            options.Add(LayoutHooks.Head.Last,
            typeof(AbpApplicationPathViewComponent));
        });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context)
    {
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });
    }

    private void ConfigureServicesLegado(IConfiguration configuration, IServiceCollection services)
    {
        var connectionString = configuration.GetConnectionString("UfepConnection");

        services.AddScoped<IDbConnectionWrapper>(provider =>
            new SqlConnectionWrapper(connectionString)
         );

        services.AddScoped<IDbConnection>(provider =>
        {
            return new SqlConnection(connectionString);
        });

        services.AddScoped<IDbTransaction>(provider =>
        {
            var connection = provider.GetRequiredService<IDbConnection>();

            if (connection.State != ConnectionState.Open)
                connection.Open();

            return connection.BeginTransaction();

        });

        #region Configure SOAP Services

        services.Configure<AutenticacaoConsultaCPFConfig>(configuration.GetSection("AutenticacaoConsultaCPF"));
        services.AddScoped<ConsultaCPFClient>();
        services.AddScoped<ConsultaCNPJClient>();
        #endregion
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                    bundle.AddFiles("/css/font-bundle.css");
                    bundle.AddFiles("/css/perfect-scrollbar.css");
                    bundle.AddFiles("/css/bootstrap-icons.css");
                    bundle.AddFiles("/css/layout-bundle.css");
                    bundle.AddFiles("/css/js-bundle.css");
                    bundle.AddFiles("/css/main.min.css");
                    bundle.AddFiles("/css/style.min.css");
                    bundle.AddFiles("/css/select2.min.css");
                    bundle.AddFiles("/css/select2-leptonx-bundle.css");
                    bundle.AddFiles("/css/datatables.net-select-dt/select.datatables.min.css");
                }
            );

            options.StyleBundles.Configure(
                SISPRECLeptonXLiteThemeBundles.Styles.Light,
                bundle =>
                {
                    bundle.AddFiles("/css/bootstrap-light.css");
                    bundle.AddFiles("/css/light.css");
                }
            );

            options.StyleBundles.Configure(
                SISPRECLeptonXLiteThemeBundles.Styles.Dark,
                bundle =>
                {
                    bundle.AddFiles("/css/bootstrap-dark.css");
                    bundle.AddFiles("/css/dark.css");
                }
            );
            options.ScriptBundles.Configure(
                LeptonXLiteThemeBundles.Scripts.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                    bundle.AddFiles("/js/cadastroJustificativa/cadastro-justificativa.js");
                    bundle.AddFiles("/js/cadastroJustificativa/cadastro-justificativa-class.js");
                    bundle.AddFiles("/js/cadastroJustificativa/cadastro-justificativa-helper.js");
                    bundle.AddFiles("/js/comparacao.js");
                    bundle.AddFiles("/js/perfect-scrollbar.min.js");
                    bundle.AddFiles("/js/lepton-x.bundle.min.js");
                    bundle.AddFiles("/js/main.min.js");
                    bundle.AddFiles("/js/head.bundle.min.js");
                    bundle.AddFiles("/js/dataTables.rowGroup.min.js");
                    bundle.AddFiles("/js/datatables.net-select/datatables.select.min.js");
                }
            );

            options.ScriptBundles.Configure(nameof(GlobalizationValidatorBundleContributor), bundle =>
            {
                bundle.AddContributors(typeof(GlobalizationValidatorBundleContributor));
            });
        });

        Configure<AbpBundlingOptions>(options =>
        {
            options.Mode = BundlingMode.None;
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<SISPRECWebModule>();
        });
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        if (hostingEnvironment.IsDevelopment())
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.ReplaceEmbeddedByPhysical<SISPRECDomainSharedModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TRF3.SISPREC.Domain.Shared"));
                options.FileSets.ReplaceEmbeddedByPhysical<SISPRECDomainModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TRF3.SISPREC.Domain"));
                options.FileSets.ReplaceEmbeddedByPhysical<SISPRECApplicationContractsModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TRF3.SISPREC.Application.Contracts"));
                options.FileSets.ReplaceEmbeddedByPhysical<SISPRECApplicationModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TRF3.SISPREC.Application"));
                options.FileSets.ReplaceEmbeddedByPhysical<SISPRECWebModule>(hostingEnvironment.ContentRootPath);
                //options.FileSets.ReplaceEmbeddedByPhysical<SISPRECInfraestruturaModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}TRF3.SISPREC.Infraestrutura"));
            });
        }

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<SISPRECInfraestruturaModule>();
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new SISPRECMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new SISPRECThemeToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(SISPRECApplicationModule).Assembly);
            //Para adicionar novo projeto de AppServices
            options.ConventionalControllers.Create(typeof(SISPRECProcessaPrecatorioApplicationModule).Assembly);
            options.ConventionalControllers.FormBodyBindingIgnoredTypes.Add(typeof(CreateFileInput));
        });
    }

    private void ConfigureSwaggerServices(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(
            options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "SISPREC API", Version = "v1" });
                options.DocInclusionPredicate((docName, description) => true);
                options.CustomSchemaIds(type => type.FullName);
                // Adiciona uma regra para resolver ações conflitantes
                options.ResolveConflictingActions(apiDescriptions => apiDescriptions.Last());
                options.HideAbpEndpoints();
            }
        );
    }

    public override void OnPostApplicationInitialization(ApplicationInitializationContext context)
    {
        RegistrarWorkers(context);
        var jobSincronizaDominioAgendado = context.ServiceProvider.GetService<JobSincronizaDominioAgendado>()!;
        jobSincronizaDominioAgendado.Agendar();
    }


    private static void AlterarControlesParadosProcessandoParaInterrompidos(ApplicationInitializationContext context)
    {
        IControleProcessamentoManager controleManager = context.ServiceProvider.GetRequiredService<IControleProcessamentoManager>();
        IBackgroundWorkersService backgroundWorkerService = context.ServiceProvider.GetRequiredService<IBackgroundWorkersService>();

        var servicosTransmisscaoCjf = new string[] {
            ServicosBackground.Extracao,
            ServicosBackground.Enriquecimento,
            ServicosBackground.Importacao,
            ServicosBackground.Validacao,
            ServicosBackground.Envio
        };

        foreach (var servico in servicosTransmisscaoCjf)
        {
            Type tipoServico = ServicosBackground.ObterTipoServico(servico);
            IBaseBackgroundWorker worker = backgroundWorkerService.ObterBackgroundWorker(tipoServico);

            var etapa = ServicosBackground.ObterEtapaDoServico(servico);

            if (worker.IsAtivo())
                worker.Interromper();
        }
        controleManager.AlterarStatusControlesInicializarAplicacao();
    }

    private static void RegistrarWorkers(ApplicationInitializationContext context)
    {
        if (!context.ServiceProvider.GetRequiredService<IConfiguration>().IsTestEnvironment())
        {
            context.AddBackgroundWorkerAsync<ExtracaoPrecatorioWorker>();
            context.AddBackgroundWorkerAsync<ImportacaoPrecatorioWorker>();
            context.AddBackgroundWorkerAsync<EnriquecimentoPrecatorioWorker>();
            context.AddBackgroundWorkerAsync<ValidacaoPrecatorioWorker>();
            context.AddBackgroundWorkerAsync<EnvioPrecatorioWorker>();
        }
    }

    private static void AplicarMigrations(ServiceConfigurationContext context)
    {
        try
        {
            context.Services.AddDbContext<SISPRECDbContext>(
                options => options.UseSqlServer(
                                        context.Services.GetConfiguration().GetConnectionString("Default")!,
                                        sqlOptions =>
                                        {
                                            sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                                            sqlOptions.CommandTimeout((int)TimeSpan.FromMinutes(1).TotalSeconds);
                                        }));

            Console.WriteLine($"--------- Aplicando migrations...");

            // Cria o provedor de serviços
            var serviceProvider = context.Services.BuildServiceProvider();

            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<SISPRECDbContext>();
            dbContext.Database.MigrateAsync().Wait();
            // Opcional: Seed Data
            Console.WriteLine($"--------- Migrations aplicadas com sucesso.");
        }
        catch (Exception ex)
        {
            // Logar a exceção ou lidar de acordo
            Console.WriteLine($"Erro ao aplicar migrações: {ex.Message}");
            throw;
        }
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        AlterarControlesParadosProcessandoParaInterrompidos(context);
        IApplicationBuilder app = context.GetApplicationBuilder();
        IWebHostEnvironment env = context.GetEnvironment();
        IConfiguration configuration = context.GetConfiguration();

        var pathBase = SisprecUrlHelper.
        GetPathBase(context?.ServiceProvider?.GetService<IConfiguration>(), true, false);

        app.UsePathBase(new PathString(pathBase));
        app.UseCookiePolicy(); // added this, Before UseAuthentication or anything else that writes cookies.
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseAbpSwaggerUI(options =>
            {
                options.SwaggerEndpoint(pathBase + "/swagger/v1/swagger.json", "SISPREC API");
            });
        }

        app.UseAbpRequestLocalization(options =>
        {
            var supportedCultures = new[] { new CultureInfo("pt-BR") };
            options.DefaultRequestCulture = new RequestCulture("pt-BR");
            options.SupportedCultures = supportedCultures;
            options.SupportedUICultures = supportedCultures;
            options.RequestCultureProviders = new List<IRequestCultureProvider>
        {
            new QueryStringRequestCultureProvider(),
            new CookieRequestCultureProvider()
        };
        });

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
        }

        app.UseCorrelationId();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAuthentication();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();

        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();
        app.Use(async (context, next) =>
        {
            if (context.Request.Path
           .ToString().EndsWith("/Account/Logout", System.StringComparison.OrdinalIgnoreCase))
            {
                await context.SignOutAsync();
            }
            await next();
        });
    }
}
