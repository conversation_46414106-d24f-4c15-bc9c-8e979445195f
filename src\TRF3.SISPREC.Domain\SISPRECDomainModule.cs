using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.MultiTenancy;
using EasyAbp.ProcessManagement;
using Volo.Abp.Emailing;
using Volo.Abp.Localization;
using Volo.Abp.MailKit;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC;


[ExcludeFromCodeCoverage]
[DependsOn(
    typeof(ProcessManagementDomainModule),
    typeof(SISPRECDomainSharedModule),
    typeof(AbpEmailingModule),
    typeof(AbpSettingManagementDomainModule),
    typeof(AbpMailKitModule)
)]
public class SISPRECDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Languages.Add(new LanguageInfo("pt-BR", "pt-BR", "Português"));
        });

        Configure<AbpMultiTenancyOptions>(options =>
        {
            options.IsEnabled = MultiTenancyConsts.IsEnabled;
        });

#if DEBUG
        context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
#else
        context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, MailKitSmtpEmailSender>());
#endif
    }
}
