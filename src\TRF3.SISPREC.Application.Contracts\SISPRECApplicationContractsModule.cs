using System.Diagnostics.CodeAnalysis;
using EasyAbp.ProcessManagement;
using Volo.Abp.Modularity;
using Volo.Abp.ObjectExtending;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC;

[ExcludeFromCodeCoverage]
[DependsOn(
    typeof(ProcessManagementApplicationContractsModule),
    typeof(SISPRECDomainSharedModule),
    typeof(AbpSettingManagementApplicationContractsModule),
    typeof(AbpObjectExtendingModule)
)]
public class SISPRECApplicationContractsModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        DtoExtensions.Configure();
    }
}
