<Project Sdk="Microsoft.NET.Sdk.Web">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC.Web</RootNamespace>
		<AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
		<GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
		<MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
		<PreserveCompilationReferences>true</PreserveCompilationReferences>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
		<UserSecretsId>2d689870-cfa8-4131-9216-22c8889cd9f8</UserSecretsId>
	</PropertyGroup>

	<!-- Modo Release: Copiar e substituir appsettings.Release.json como appsettings.json -->
	<ItemGroup Condition="'$(Configuration)' == 'Release'">
		<!-- Excluir o appsettings.json original do build em modo Release -->
		<Content Remove="appsettings.json" />
		<Content Remove="appsettings.Development.json" />

		<!-- Copiar appsettings.Release.json para appsettings.json -->
		<Content Update="appsettings.Release.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<TargetPath>appsettings.json</TargetPath>
			<!-- Renomeia no destino -->
		</Content>
	</ItemGroup>

	<!-- Para Debug, copiar normalmente todos os arquivos appsettings -->
	<ItemGroup Condition="'$(Configuration)' == 'Debug'">
		<Content Update="appsettings*.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
    
	<!-- Remover o appsettings.Release.json do publish para evitar conflitos -->
	<ItemGroup>
		<None Remove="appsettings.Release.json" Condition="'$(Configuration)' == 'Release'" />
	</ItemGroup>
	<ItemGroup>
	  <None Include="wwwroot\js\cadastroJustificativa\cadastro-justificativa.js" />
	  <None Include="wwwroot\js\cadastroJustificativa\cadastro-justificativa-helper.js" />
	  <None Include="wwwroot\js\cadastroJustificativa\cadastro-justificativa-class.js" />
	  <None Include="wwwroot\js\historicoComponent.js" />
	  <None Include="wwwroot\js\util.js" />
	</ItemGroup>

	<!-- Sempre copiar os arquivos JS e CSS -->
	<ItemGroup>
		<None Update="Pages\**\*.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Pages\**\*.css">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<!-- Dependências e referências de pacotes -->
	<ItemGroup>
		<PackageReference Include="EasyAbp.ProcessManagement.Web" Version="1.1.0" />
		<PackageReference Include="FluentResults" Version="3.15.2" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Autofac" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Swashbuckle" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Web" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Web" Version="8.3.4" />
		<ProjectReference Include="..\TRF3.SISPREC.HttpApi\TRF3.SISPREC.HttpApi.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.EntityFrameworkCore\TRF3.SISPREC.EntityFrameworkCore.csproj" />
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="3.3.4" />
		<PackageReference Include="Volo.Abp.LeptonXLiteTheme.SourceCode" Version="3.3.4" />
		<PackageReference Include="Volo.Abp.Http.Client.IdentityModel.Web" Version="8.3.4" />
	</ItemGroup>
	<ItemGroup>
		<Content Update="Pages\Settings\RequisicoesVerificacoes\Index.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

</Project>
