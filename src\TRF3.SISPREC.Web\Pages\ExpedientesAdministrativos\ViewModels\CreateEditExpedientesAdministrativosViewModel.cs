using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos.ViewModels;

[ExcludeFromCodeCoverage]
public class CreateEditExpedientesAdministrativosViewModel
{
    [HiddenInput]
    public int ExpedientesAdministrativosId { get; set; }
    [Required]
    [Display(Name = "Tipo")]
    [SelectItems(nameof(TipoExpedienteLookupList))]
    public ETipoExpedienteAdministrativo TipoExpedienteAdministrativo { get; set; }
    public List<SelectListItem> TipoExpedienteLookupList { get; set; } = [.. Enum.GetValues(typeof(ETipoExpedienteAdministrativo))
       .Cast<ETipoExpedienteAdministrativo>()
       .Select(e => new SelectListItem
       {
           Value = e.ToString(),
           Text = e.GetEnumDescription()
       })];

    [Required]
    [Display(Name = "Tipo Processo SEI")]
    [SelectItems(nameof(TipoProcessoSeiLookupList))]
    public ETipoProcessoSei TipoProcessoSEI { get; set; }
    public List<SelectListItem> TipoProcessoSeiLookupList { get; set; } = [.. Enum.GetValues(typeof(ETipoProcessoSei))
        .Cast<ETipoProcessoSei>()
        .Select(e => new SelectListItem
        {
            Value = e.ToString(),
            Text = e.GetEnumDescription()
        })];

    [Display(Name = "Motivo (utilize um Motivo pré-preenchido e/ou digite livremente)")]
    [Required]
    [SelectItems(nameof(MotivoLookupList))]
    public int? MotivoId { get; set; }
    public List<SelectListItem> MotivoLookupList { get; set; } =
    [
        new SelectListItem { Value = "", Text = "" }
    ];
    [Display(Name = "")]
    public string? ObservacaoExpedienteAdministrativo { get; set; }
    [Display(Name = "Requisições")]
    public string? Requisicao { get; set; }
    [HiddenInput]
    public string? Requisicoes { get; set; }

}
