﻿<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\common.props" />

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <RootNamespace>TRF3.SISPREC</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>cecb5ba5-744d-4e0c-96e8-3e6dd9ecc788</UserSecretsId>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="EasyAbp.ProcessManagement.Domain" Version="1.1.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.2" />
        <PackageReference Include="Volo.Abp.DDD.Domain" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.TenantManagement.Domain" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.MailKit" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.Emailing" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="8.3.4" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\TRF3.SISPREC.Domain.Shared\TRF3.SISPREC.Domain.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="BlocosSisprec\Servicos\" />
      <Folder Include="ExpedientesAdministrativosHistorico\Servicos\" />
    </ItemGroup>

</Project>
