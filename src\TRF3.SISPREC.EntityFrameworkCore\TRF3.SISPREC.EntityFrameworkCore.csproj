<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC</RootNamespace>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="Migrations\Sql\AJUSTE_MOTIVO_OCORRENCIA.sql" />
	  <None Remove="Migrations\Sql\ATUALIZA_VIEWS.sql" />
	  <None Remove="Migrations\Sql\ATUALIZA_VIEWS_NOLOCK.sql" />
	  <None Remove="Migrations\Sql\CARGA_CODIGO_RECEITA_FEDERAL.sql" />
	  <None Remove="Migrations\Sql\CARGA_VERIFICACAO_PREVENCAO.sql" />
	  <None Remove="Migrations\Sql\INSERT_OCORRENCIA_MOTIVO.sql" />
	  <None Remove="Migrations\Sql\INSERT_PREVENCAO_11_12.sql" />
	  <None Remove="Migrations\Sql\INSERT_PREVENCAO_TIPO_22.sql" />
	  <None Remove="Migrations\Sql\INSERT_PREVENCAO_TIPO_23.sql" />
	  <None Remove="Migrations\Sql\INSERT_PREVENCAO_TIPO_24.sql" />
	  <None Remove="Migrations\Sql\INSERT_VERIFICACAO_SUCUMBENCIAL.sql" />
	  <None Remove="Migrations\Sql\REMOVE_CONSTRAINSTS_UNIDADE.sql" />
	  <None Remove="Migrations\Sql\INSERT_PREVENCAO_TIPO_21.sql" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="EasyAbp.ProcessManagement.EntityFrameworkCore" Version="1.1.0" />
		<ProjectReference Include="..\TRF3.SISPREC.Domain\TRF3.SISPREC.Domain.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.ProcessaPrecatorio.Domain\TRF3.SISPREC.ProcessaPrecatorio.Domain.csproj" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.EntityFrameworkCore" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="8.3.4" />
	</ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
            <PrivateAssets>compile; contentFiles; build; buildMultitargeting; buildTransitive; analyzers; native</PrivateAssets>
        </PackageReference>
    </ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Migrations\Sql\AJUSTE_MOTIVO_OCORRENCIA.sql" />
		<EmbeddedResource Include="Migrations\Sql\CARGA_UNIDADES.sql" />
		<EmbeddedResource Include="Migrations\Sql\ATUALIZA_VIEWS_NOLOCK.sql" />
		<EmbeddedResource Include="Migrations\Sql\CARGA_CODIGO_RECEITA_FEDERAL.sql" />
		<EmbeddedResource Include="Migrations\Sql\CARGA_INDICADOR_ECONOMICO.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="Migrations\Sql\ATUALIZA_VIEWS.sql" />
		<EmbeddedResource Include="Migrations\Sql\CARGA_VERIFICACAO_PREVENCAO.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_11_12.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_24.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_22.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_32.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_VERIFICACAO_ORGAO_PSS.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_35.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_34.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_23.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_31.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_PREVENCAO_TIPO_21.sql" />
		<EmbeddedResource Include="Migrations\Sql\INSERT_VERIFICACAO_SUCUMBENCIAL.sql" />
		<EmbeddedResource Include="Migrations\Sql\MIGRACAO_INICIAL.sql" />
		<EmbeddedResource Include="Migrations\Sql\CARGA_INICIAL.sql" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Include="Migrations\Sql\INSERT_OCORRENCIA_MOTIVO.sql" />
	  <EmbeddedResource Include="Migrations\Sql\REMOVE_CONSTRAINSTS_UNIDADE.sql" />
	</ItemGroup>
</Project>
