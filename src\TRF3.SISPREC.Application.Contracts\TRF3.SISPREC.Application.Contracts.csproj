﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC</RootNamespace>
		<UserSecretsId>68b79598-0f78-43e7-b7ff-279c7644f30d</UserSecretsId>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="EasyAbp.ProcessManagement.Application.Contracts" Version="1.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
		<PackageReference Include="Volo.Abp.ObjectExtending" Version="8.3.4" />
		<ProjectReference Include="..\TRF3.SISPREC.Domain\TRF3.SISPREC.Domain.csproj" />
		<PackageReference Include="Volo.Abp.Ddd.Application.Contracts" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="8.3.4" />
	</ItemGroup>

</Project>
