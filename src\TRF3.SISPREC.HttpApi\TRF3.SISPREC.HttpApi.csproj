﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC</RootNamespace>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="EasyAbp.ProcessManagement.HttpApi" Version="1.1.0" />
		<PackageReference Include="Volo.Abp.AspNetCore" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Http" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="8.3.4" />
		<ProjectReference Include="..\TRF3.SISPREC.Application\TRF3.SISPREC.Application.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.Domain.Shared\TRF3.SISPREC.Domain.Shared.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.ProcessaPrecatorio.Application\TRF3.SISPREC.ProcessaPrecatorio.Application.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.SincronizacaoLegado\TRF3.SISPREC.SincronizacaoLegado.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.SincronizaDominio.Application\TRF3.SISPREC.SincronizaDominio.Application.csproj" />
	</ItemGroup>

</Project>
