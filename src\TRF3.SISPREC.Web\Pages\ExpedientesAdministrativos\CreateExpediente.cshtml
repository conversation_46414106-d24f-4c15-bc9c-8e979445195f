@page
@using TRF3.SISPREC.Web
@using TRF3.SISPREC.Web.Menus
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@model TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos.CreateExpedienteModel
@inject IPageLayout PageLayout
@{
	PageLayout.Content.Title = "Inserir Expediente Administrativo";
	PageLayout.Content.MenuItemName = SISPRECMenus.ExpedienteAdministrativoListagem;
}
@section scripts {
	<abp-script src="/Pages/ExpedientesAdministrativos/create.js" />
}
@section styles {
	<abp-style src="/Pages/ExpedientesAdministrativos/create.css" />
}

<abp-card>
	<abp-card-body>
		<form abp-model="ViewModel" data-ajaxForm="true" id="ExpedienteForm">

			<abp-row>
				<abp-column class="col-md-6">
					<abp-select asp-for="ViewModel.TipoExpedienteAdministrativo" asp-items="@Model.ViewModel.TipoExpedienteLookupList" />
				</abp-column>
				<abp-column class="col-md-6">
					<abp-select asp-for="ViewModel.TipoProcessoSEI" asp-items="@Model.ViewModel.TipoProcessoSeiLookupList" />
				</abp-column>
			</abp-row>
			<abp-row>
				<abp-column class="col-md-9">
					<abp-select asp-for="ViewModel.MotivoId" asp-items="@Model.ViewModel.MotivoLookupList" />
				</abp-column>

				<abp-column class="col-md-3 d-flex flex-column justify-content-center">
					<abp-button button-type="Primary" class="btn btn-sm" id="addMotivo">+ Novo</abp-button>
				</abp-column>

				<abp-column class="col-md-12 mt-2">
					<abp-input asp-for="ViewModel.ObservacaoExpedienteAdministrativo" />
				</abp-column>
			</abp-row>


			<label asp-for="ViewModel.Requisicao" class="form-label"></label>
			<abp-row class="align-items-end">
				<abp-column class="col-md-6">
					<input type="text" id="newItemText" placeholder="nº de requisição" class="form-control" />
				</abp-column>
				<abp-column class="col-md-3">
					<div class="d-flex gap-1">
						<button type="button" class="btn btn-primary btn-sm" id="addRequisicao">+</button>
						<button type="button" class="btn btn-primary btn-sm" disabled id="removeRequisicao">-</button>
					</div>
				</abp-column>
			</abp-row>


			<abp-table striped-rows="true" class="table table-bordered mt-3" id="tabelaRequisicoes">
				<thead>
					<tr>
						<th style="width: 90px" scope="Column">Nº REQUISIÇÃO</th>
						<th style="width: 110px" scope="Column">CPF/CNPJ</th>
						<th style="width: 250px" scope="Column">REQUERENTE</th>
						<th style="width: 90px" scope="Column">CÓD. SIAFI JUÍZO</th>
						<th style="width: 250px" scope="Column">JUÍZO</th>
					</tr>
				</thead>
				<tbody id="listaRequisicoes">
				</tbody>
			</abp-table>

			<input type="hidden" asp-for="ViewModel.Requisicoes" id="RequisicoesHiddenInput" />

			<abp-button button-type="Outline_Primary" id="btnCancelar">Cancelar</abp-button>
			<abp-button button-type="Primary" id="btnSalvar" type="submit">
				<i class="fa fa-check me-1"></i> Salvar
			</abp-button>
		</form>
	</abp-card-body>
</abp-card>
