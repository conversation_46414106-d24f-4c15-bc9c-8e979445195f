﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC</RootNamespace>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
		<ImplicitUsings>enable</ImplicitUsings>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="DeepCloner" Version="0.10.4" />
		<PackageReference Include="EasyAbp.ProcessManagement.Domain.Shared" Version="1.1.0" />
		<PackageReference Include="Volo.Abp.Autofac" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.DDD.Domain.Shared" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.VirtualFileSystem" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Validation" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Serialization" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Security" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.RemoteServices" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.ObjectMapping" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.ObjectExtending" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.MultiTenancy" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.Localization" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Domain.Shared" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.BackgroundJobs.Quartz" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.BackgroundWorkers.Quartz" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="8.3.4" />
		<PackageReference Include="Quartz.Serialization.Json" Version="3.7.0" />
		<PackageReference Include="Quartz.Plugins" Version="3.7.0" />
        <PackageReference Include="Flunt.Br" Version="2.0.0" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Localization\SISPREC\*.json" />
		<Content Remove="Localization\SISPREC\*.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="System.Data.OleDb" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.4" />
	</ItemGroup>

</Project>
