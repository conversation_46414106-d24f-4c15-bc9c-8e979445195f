﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>TRF3.SISPREC</RootNamespace>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="EasyAbp.ProcessManagement.Application" Version="1.1.0" />
		<ProjectReference Include="..\TRF3.SISPREC.Domain\TRF3.SISPREC.Domain.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.Application.Contracts\TRF3.SISPREC.Application.Contracts.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.Infraestrutura\TRF3.SISPREC.Infraestrutura.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.ProcessaPrecatorio.Domain\TRF3.SISPREC.ProcessaPrecatorio.Domain.csproj" />
		<ProjectReference Include="..\TRF3.SISPREC.SincronizaDominio.Application\TRF3.SISPREC.SincronizaDominio.Application.csproj" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
		<PackageReference Include="System.Data.OleDb" Version="8.0.0" />
		<PackageReference Include="Volo.Abp.Ddd.Application" Version="8.3.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Application" Version="8.3.4" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Analises\" />
	</ItemGroup>
	
</Project>
