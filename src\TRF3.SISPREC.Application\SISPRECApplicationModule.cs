using System.Diagnostics.CodeAnalysis;
using EasyAbp.ProcessManagement;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC;

[ExcludeFromCodeCoverage]
[DependsOn(
    typeof(ProcessManagementApplicationModule),
    typeof(SISPRECDomainModule),
    typeof(SISPRECApplicationContractsModule),
    typeof(SISPRECSincDominioApplicationModule),
    typeof(SISPRECInfraestruturaModule),
    typeof(AbpSettingManagementApplicationModule)
    )]
public class SISPRECApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<SISPRECApplicationModule>();
        });

    }
}
