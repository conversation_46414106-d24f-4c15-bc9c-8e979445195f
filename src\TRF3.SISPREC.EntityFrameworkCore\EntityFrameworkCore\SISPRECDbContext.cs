using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using EasyAbp.ProcessManagement.EntityFrameworkCore;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AcoesJustificativa;
using TRF3.SISPREC.AcoesOriginarias;
using TRF3.SISPREC.AdvogadosJudiciais;
using TRF3.SISPREC.Agencias;
using TRF3.SISPREC.AntecessoresBeneficiarios;
using TRF3.SISPREC.Assuntos;
using TRF3.SISPREC.Bancos;
using TRF3.SISPREC.BeneficiarioIdentificacaoTipos;
using TRF3.SISPREC.Beneficiarios;
using TRF3.SISPREC.BeneficiariosOrigensPCT;
using TRF3.SISPREC.BeneficiariosOrigensRPV;
using TRF3.SISPREC.BeneficiarioSucessaoTipos;
using TRF3.SISPREC.BeneficiarioTipos;
using TRF3.SISPREC.CidadesRegPag;
using TRF3.SISPREC.CodigosReceitaFederal;
using TRF3.SISPREC.Configuration;
using TRF3.SISPREC.ContaBancariaBeneficiarios;
using TRF3.SISPREC.ContaBancarias;
using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.ControleProcessamentoArquivos;
using TRF3.SISPREC.ControleProcessamentoProcessos;
using TRF3.SISPREC.ControleProcessamentos;
using TRF3.SISPREC.DespesaClassificacoes;
using TRF3.SISPREC.DespesaNaturezas;
using TRF3.SISPREC.DespesaTipos;
using TRF3.SISPREC.Domain;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.Fases;
using TRF3.SISPREC.FaseTipos;
using TRF3.SISPREC.IndiceAtualizacaoMonetarias;
using TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos;
using TRF3.SISPREC.LogDetalhes;
using TRF3.SISPREC.LogGerais;
using TRF3.SISPREC.MovimentoTipos;
using TRF3.SISPREC.Municipios;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.OrdemPagamento107aTipos;
using TRF3.SISPREC.PagoBeneficiariosOrigensPCT;
using TRF3.SISPREC.PagoBeneficiariosOrigensRPV;
using TRF3.SISPREC.Parcelas;
using TRF3.SISPREC.Partes;
using TRF3.SISPREC.Pessoas;
using TRF3.SISPREC.Pessoas.EnderecosPessoas;
using TRF3.SISPREC.Pessoas.SinPessoasReqPag;
using TRF3.SISPREC.Planos;
using TRF3.SISPREC.PrecatoriosOrigensPCT;
using TRF3.SISPREC.PrecatoriosOrigensRPV;
using TRF3.SISPREC.Processos;
using TRF3.SISPREC.ProcessosAnteriores;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoObservacoes;
using TRF3.SISPREC.RequisicaoRequerenteCompensados;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.REquisicoesExpedientesAdministrativos;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesPartesContratuais;
using TRF3.SISPREC.RequisicoesPartesRequerentes;
using TRF3.SISPREC.RequisicoesPartesRequerentesIR;
using TRF3.SISPREC.RequisicoesPartesRequerentesPSS;
using TRF3.SISPREC.RequisicoesPartesReus;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes;
using TRF3.SISPREC.SentencaTipos;
using TRF3.SISPREC.ServidorCondicaoTipos;
using TRF3.SISPREC.SincronizacaoProgressos;
using TRF3.SISPREC.SincronizacoesDominios;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.TiposEtapaProcessamentos;
using TRF3.SISPREC.TiposProcedimentos;
using TRF3.SISPREC.UnidadeJudicialTipoNaturezas;
using TRF3.SISPREC.UnidadeJudicialTipos;
using TRF3.SISPREC.UnidadesGestoras;
using TRF3.SISPREC.UnidadesJudiciais;
using TRF3.SISPREC.ValorTipos;
using TRF3.SISPREC.VerificacaoTipos;
using TRF3.SISPREC.ViewAuditoriaEntidades;
using TRF3.SISPREC.ViewAuditoriaPropriedades;
using TRF3.SISPREC.ViewControles;
using TRF3.SISPREC.ViewFases;
using TRF3.SISPREC.ViewProcessos;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using TRF3.SISPREC.ModelosDocumentos;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos;
using TRF3.SISPREC.RequisicaoRequerenteParcelas;
using TRF3.SISPREC.RequisicaoContratualParcelas;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.UnidadesEquivalentes;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.IndicadorEconomicos;
using TRF3.SISPREC.UnidadesOrcamentarias;
using TRF3.SISPREC.Unidades;
using TRF3.SISPREC.Peritos;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.JustificativaComparacoes;
using TRF3.SISPREC.JustificativaDocumentos;
using TRF3.SISPREC.RequisicoesPropostaParcela;
using TRF3.SISPREC.Setores;
using TRF3.SISPREC.MotivosExpedientesAdministrativos;

namespace TRF3.SISPREC.EntityFrameworkCore;

[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ConnectionStringName("Default")]
[ExcludeFromCodeCoverage]
public class SISPRECDbContext :
    AbpDbContext<SISPRECDbContext>,
    ITenantManagementDbContext,
    IDbContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */

    #region Entities from the modules

    /* Notice: We only implemented IIdentityDbContext and ITenantManagementDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityDbContext and ITenantManagementDbContext.
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    ////Identity
    //public DbSet<IdentityUser> Users { get; set; }
    //public DbSet<IdentityRole> Roles { get; set; }
    //public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    //public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    //public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    //public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    //public DbSet<IdentityUserDelegation> UserDelegations { get; set; }

    // Tenant Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

    #endregion
    public DbSet<Assunto> Assuntos { get; set; }
    public DbSet<SincronizacaoProgresso> SincronizacaoProgressos { get; set; }
    public DbSet<AntecessorBeneficiario> AntecessorBeneficiarios { get; set; }
    public DbSet<AcaoOriginaria> AcaoOriginarias { get; set; }
    public DbSet<IndiceAtualizacaoMonetariaTipo> IndiceAtualizacaoMonetariaTipos { get; set; }
    public DbSet<LogGeral> LogGerals { get; set; }
    public DbSet<LogDetalhe> LogDetalhes { get; set; }
    public DbSet<Beneficiario> Beneficiarios { get; set; }
    public DbSet<IndiceAtualizacaoMonetaria> IndiceAtualizacaoMonetarias { get; set; }
    public DbSet<UnidadeGestora> UnidadeGestoras { get; set; }
    public DbSet<OrdemPagamento107aTipo> OrdemPagamento107aTipos { get; set; }
    public DbSet<Parcela> Parcelas { get; set; }
    public DbSet<Parte> Partes { get; set; }
    /// <summary>
    /// Controle de processamento de um processo (ciclo de vida inteiro do processo no sistema).        Configurações dessa classe do contexto: <see cref="Acesso.Configuration.ControleProcessamentoProcessoConfiguration"/>
    /// </summary>
    public DbSet<ControleProcessamentoProcesso> ControleProcessamentoProcessos { get; set; }
    public DbSet<UnidadeOrcamentaria> UnidadesOrcamentaria { get; set; }
    public DbSet<ContaBancaria> ContaBancarias { get; set; }
    public DbSet<MovimentoTipo> MovimentoTipos { get; set; }
    public DbSet<SentencaTipo> SentencaTipos { get; set; }
    /// <summary>
    /// Tipo da etapa do processamento.
    /// </summary>
    public DbSet<TipoEtapaProcessamento> TipoEtapaProcessamentos { get; set; }
    /// <summary>
    /// Arquivo de dados do processamento de um lote de processos (MDB fornecido pela UFEP).        Configurações dessa classe do contexto: <see cref="Acesso.Configuration.ControleProcessamentoProcessoArquivoConfiguration"/>
    /// </summary>
    public DbSet<ControleProcessamentoArquivo> ControleProcessamentoArquivos { get; set; }
    public DbSet<BeneficiarioSucessaoTipo> BeneficiarioSucessaoTipos { get; set; }
    public DbSet<DespesaTipo> DespesaTipos { get; set; }
    public DbSet<UnidadeJudicial> UnidadeJudiciais { get; set; }
    public DbSet<UnidadeJudicialOrigem> UnidadeJudiciaisOrigens { get; set; }
    public DbSet<ProcessoAnterior> ProcessoAnteriors { get; set; }
    /// <summary>
    /// Processo.        Configuração do banco de dados: <see cref="ProcessoConfiguration"/>
    /// </summary>
    public DbSet<Processo> Processos { get; set; }
    public DbSet<Fase> Fases { get; set; }
    public DbSet<UnidadeJudicialTipo> UnidadeJudicialTipos { get; set; }
    /// <summary>
    /// Representa o controle de processamento de uma etapa/fase.
    /// </summary>
    public DbSet<ControleProcessamento> ControleProcessamentos { get; set; }
    public DbSet<ContaBancariaBeneficiario> ContaBancariaBeneficiarios { get; set; }
    public DbSet<DespesaNatureza> DespesaNaturezas { get; set; }
    public DbSet<DespesaClassificacao> DespesaClassificacaos { get; set; }
    public DbSet<FaseTipo> FaseTipos { get; set; }
    public DbSet<Plano> Planos { get; set; }
    public DbSet<BeneficiarioOrigemPCT> BeneficiarioOrigemPCTs { get; set; }
    public DbSet<BeneficiarioOrigemRPV> BeneficiarioOrigemRPVs { get; set; }
    public DbSet<SincronizacaoDominio> SincronizacaoDominios { get; set; }
    public DbSet<ValorTipo> ValorTipos { get; set; }
    public DbSet<UnidadeJudicial> UnidadesJudiciais { get; set; }
    public DbSet<BeneficiarioTipo> BeneficiarioTipos { get; set; }
    public DbSet<BeneficiarioIdentificacaoTipo> BeneficiarioIdentificacaoTipos { get; set; }
    public DbSet<SincronizacaoDominio> SincronizacoesDominios { get; set; }

    #region DbSet Arquivo MDB

    /// <summary>
    /// dbset utilizado para importação e exportação do mdb
    /// </summary>
    public DbSet<PagoBeneficiarioOrigemPCT> PagoBeneficiariosPCT { get; set; }

    /// <summary>
    /// dbset utilizado para importação e exportação do mdb
    /// </summary>
    public DbSet<PagoBeneficiarioOrigemRPV> PagoBeneficiariosRPV { get; set; }
    public DbSet<PrecatorioOrigemPCT> PrecatorioOrigemPCT { get; set; }
    public DbSet<PrecatorioOrigemRPV> PrecatorioOrigemRPV { get; set; }
    public DbSet<ServidorCondicaoTipo> ServidorCondicaoTipo { get; set; }

    #endregion


    public DbSet<ViewFase> ViewFases { get; set; }
    public DbSet<ViewControle> ViewControles { get; set; }
    public DbSet<ViewProcesso> ViewProcessos { get; set; }
    public DbSet<UnidadeJudicialTipoNatureza> UnidadeJudicialTipoNaturezas { get; set; }

    public DbSet<AcaoJustificativa> MotivoAcoes { get; set; }
    public DbSet<ViewAuditoriaEntidade> ViewAuditoriaEntidades { get; set; }
    public DbSet<ViewAuditoriaPropriedade> ViewAuditoriaPropriedades { get; set; }
    public DbSet<CidadeRegPag> CidadeReqPag { get; set; }
    public DbSet<Municipio> Municipio { get; set; }
    public DbSet<ServidorCondicaoTipo> ServidorCondicaoTipos { get; set; }
    public DbSet<AssuntoAuxiliar> AssuntoAuxiliars { get; set; }
    public DbSet<AssuntoDespesa> AssuntoDespesas { get; set; }
    public DbSet<RequisicaoProtocolo> RequisicaoProtocolos { get; set; }
    public DbSet<Proposta> Propostas { get; set; }
    public DbSet<RequisicaoProposta> RequisicaoPropostas { get; set; }
    public DbSet<SituacaoRequisicaoProtocolo> SituacaoRequisicaoProtocolos { get; set; }
    public DbSet<RequisicaoProcessoOrigem> RequisicaoProcessoOrigems { get; set; }
    public DbSet<Pessoa> Pessoas { get; set; }
    public DbSet<EnderecoPessoa> EnderecoPessoas { get; set; }
    public DbSet<AssuntoExecucao> AssuntoExecucaos { get; set; }
    public DbSet<AdvogadoJudicial> AdvogadoJudicials { get; set; }
    public DbSet<TipoProcedimento> TipoProcedimentos { get; set; }
    public DbSet<Banco> Bancos { get; set; }
    public DbSet<Agencia> Agencias { get; set; }
    public DbSet<OcorrenciaMotivo> MotivoOcorrencias { get; set; }
    public DbSet<Setor> Setores { get; set; }
    public DbSet<RequisicaoOcorrencia> RequisicaoOcorrencias { get; set; }
    public DbSet<RequisicaoParteRequerenteIr> RequisicaoParteRequerenteIr { get; set; }
    public DbSet<RequisicaoParteRequerentePss> RequisicaoParteRequerentePss { get; set; }
    public DbSet<AcaoTipo> AcaoTipo { get; set; }
    public DbSet<VerificacaoTipo> VerificacaoTipo { get; set; }
    public DbSet<RequisicaoVerificacao> requisicaoVerificacao { get; set; }
    public DbSet<CodigoReceitaFederal> CodigoReceitaFederals { get; set; }
    public DbSet<RequisicaoRequerenteCompensado> RequisicaoParteRequerenteCompensados { get; set; }
    public DbSet<OcorrenciaMotivo> OcorrenciaMotivos { get; set; }
    public DbSet<ControleImportacaoRequisicao> ControleImportacaoRequisicao { get; set; }
    public DbSet<RequisicaoParte> RequisicaoPartes { get; set; }
    public DbSet<RequisicaoParteRequerente> RequisicaoParteRequerentes { get; set; }
    public DbSet<RequisicaoParteReu> RequisicaoParteReus { get; set; }
    public DbSet<RequisicaoParteContratual> RequisicaoParteContratuais { get; set; }

    public DbSet<ExpedienteAdministrativo> ExpedientesAdministrativos { get; set; }

    public DbSet<RequisicaoExpedienteAdministrativo> RequisicaoExpedienteAdministrativo { get; set; }
    public DbSet<RequisicaoObservacao> RequisicaoObservacoes { get; set; }
    public DbSet<ModeloDocumento> ModeloDocumentos { get; set; }
    public DbSet<RequisicaoPlanoOrcamento> RequisicaoPlanoOrcamentos { get; set; }
    public DbSet<SinPessoaReqPag> SinPessoaReqPag { get; set; }
    public DbSet<RequisicaoRequerenteParcela> RequisicaoRequerenteParcelas { get; set; }
    public DbSet<RequisicaoEstorno> RequisicaoEstornos { get; set; }
    public DbSet<RequisicaoContratualParcela> RequisicaoContratualParcelas { get; set; }

    public DbSet<UnidadeEquivalente> UnidadesEquivalentes { get; set; }
    public DbSet<IndicadorEconomicoTipo> IndicadorEconomicoTipos { get; set; }
    public DbSet<IndicadorEconomico> IndicadorEconomicos { get; set; }
    public DbSet<Unidade> Unidade { get; set; }
    public DbSet<Perito> Peritos { get; set; }
    public DbSet<RequisicaoJustificativa> RequisicaoJustificativas { get; set; }
    public DbSet<JustificativaComparacao> JustificativaComparacoes { get; set; }
    public DbSet<JustificativaDocumento> RequisicaoDocumentos { get; set; }
    public DbSet<RequisicaoPropostaParcela> RequisicaoPropostaParcelas { get; set; }
    public DbSet<MotivoExpedienteAdministrativo> MotivoExpedienteAdministrativos { get; set; }

    public SISPRECDbContext(DbContextOptions<SISPRECDbContext> options)
        : base(options)
    {

    }

    internal IEnumerable<string> ExecuteSql(string sql) => Database.SqlQueryRaw<string>(sql);

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder.Conventions.Remove(typeof(ForeignKeyIndexConvention));
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ConfigureAuditLogging();
        modelBuilder.ConfigureBackgroundJobs();
        modelBuilder.ConfigureTenantManagement();
        modelBuilder.ConfigureSettingManagement();
        modelBuilder.ConfigureProcessManagement();

        foreach (var entity in modelBuilder.Model.GetEntityTypes())
        {
            //tabela
            entity.SetTableName(entity.GetTableName().ToUpper());

            //colunas
            foreach (var property in entity.GetProperties())
            {
                property.SetColumnName(property.Name.ToUpper());
            }
        }

        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }

    public async Task ExecuteNonQueryAsync(string sql)
    {
        await Database.ExecuteSqlRawAsync(sql);
    }

    public async Task SaveChangesAsync()
    {
        await Database.CommitTransactionAsync();
    }
}
