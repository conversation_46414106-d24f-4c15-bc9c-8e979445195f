using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRF3.SISPREC.Helper;
using TRF3.SISPREC.JustificativaDocumentos;
using static TRF3.SISPREC.EntityFrameworkCore.Consts.Constants;

namespace TRF3.SISPREC.EntityFrameworkCore.Configuration
{
    internal class JustificativaDocumentoConfiguration : IEntityTypeConfiguration<JustificativaDocumento>
    {
        const string NomeTabela = $"{PrefixTable.ANA}_JUSTIFICATIVA_DOCUMENTO";

        public void Configure(EntityTypeBuilder<JustificativaDocumento> builder)
        {
            builder
                .ToTable(NomeTabela);

            builder
                .HasKey(x => x.JustificativaDocumentoId)
                .HasName($"{NomeTabela}_P01");

            builder
                .Property(x => x.JustificativaDocumentoId)
                .HasColumnName("SEQ_JUSTIF_DOCUME")
                .IsRequired();

            builder
                .Property(x => x.NomeDocumento)
                .HasColumnType("varchar")
                .HasMaxLength(RequisicaoDocumentoConsts.NOME_DOCUMENTO_TAMANHO_MAX)
                .HasColumnName("NOM_DOCUME")
                .IsRequired();

            builder
                .Property(x => x.Path)
                .HasColumnType("varchar")
                .HasMaxLength(RequisicaoDocumentoConsts.PATH_TAMANHO_MAX)
                .HasColumnName("DES_PATH")
                .IsRequired();

            builder
                .Property(x => x.DataCriacao)
                .HasColumnName("DAT_CRIACA")
                .IsRequired();

            builder
                .Property(x => x.RequisicaoJustificativaId)
                .HasColumnName("SEQ_REQUIS_JUSTIF")
                .IsRequired();

            builder
                .Property(x => x.IsDeleted)
                .HasDefaultValue(false)
                .HasColumnName("SIN_EXCLUI")
                .IsRequired();

            builder
                .Property(x => x.TipoDocumentoJustificativa)
                .HasColumnType("varchar")
                .HasMaxLength(30)
                .SaveEnumAsString()
                .HasColumnName("IDE_TIPO_DOCUME")
                .IsRequired();

            builder
                .HasOne(x => x.RequisicaoJustificativa)
                    .WithMany(x => x.Documentos)
                        .HasForeignKey(x => x.RequisicaoJustificativaId)
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName($"{NomeTabela}_R01");
        }
    }
}
