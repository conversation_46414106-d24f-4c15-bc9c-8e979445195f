using TRF3.SISPREC.JustificativaDocumentos;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;
using EasyAbp.ProcessManagement.EntityFrameworkCore;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AcoesJustificativa;
using TRF3.SISPREC.AcoesOriginarias;
using TRF3.SISPREC.AdvogadosJudiciais;
using TRF3.SISPREC.Agencias;
using TRF3.SISPREC.AntecessoresBeneficiarios;
using TRF3.SISPREC.Assuntos;
using TRF3.SISPREC.AssuntosExecucoes;
using TRF3.SISPREC.Bancos;
using TRF3.SISPREC.BeneficiarioIdentificacaoTipos;
using TRF3.SISPREC.Beneficiarios;
using TRF3.SISPREC.BeneficiariosOrigensPCT;
using TRF3.SISPREC.BeneficiariosOrigensRPV;
using TRF3.SISPREC.BeneficiarioSucessaoTipos;
using TRF3.SISPREC.BeneficiarioTipos;
using TRF3.SISPREC.CidadesRegPag;
using TRF3.SISPREC.CidadesReqPag;
using TRF3.SISPREC.CodigosReceitaFederal;
using TRF3.SISPREC.ContaBancariaBeneficiarios;
using TRF3.SISPREC.ContaBancarias;
using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.ControleProcessamentoArquivos;
using TRF3.SISPREC.ControleProcessamentoProcessos;
using TRF3.SISPREC.ControleProcessamentos;
using TRF3.SISPREC.DespesaClassificacoes;
using TRF3.SISPREC.DespesaNaturezas;
using TRF3.SISPREC.DespesaTipos;
using TRF3.SISPREC.Fases;
using TRF3.SISPREC.FaseTipos;
using TRF3.SISPREC.ImportacaoRequisicoes;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.IndicadorEconomicos;
using TRF3.SISPREC.IndiceAtualizacaoMonetarias;
using TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos;
using TRF3.SISPREC.JustificativaComparacoes;
using TRF3.SISPREC.LogDetalhes;
using TRF3.SISPREC.LogGerais;
using TRF3.SISPREC.ModelosDocumentos;
using TRF3.SISPREC.MovimentoTipos;
using TRF3.SISPREC.Municipios;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.OrdemPagamento107aTipos;
using TRF3.SISPREC.Parcelas;
using TRF3.SISPREC.Partes;
using TRF3.SISPREC.Peritos;
using TRF3.SISPREC.Pessoas;
using TRF3.SISPREC.Pessoas.EnderecosPessoas;
using TRF3.SISPREC.Pessoas.SinPessoasReqPag;
using TRF3.SISPREC.Planos;
using TRF3.SISPREC.Processos;
using TRF3.SISPREC.ProcessosAnteriores;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicaoRequerenteCompensados;
using TRF3.SISPREC.RequisicoesControlesCnpjCpf;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesPartesRequerentesIR;
using TRF3.SISPREC.RequisicoesPartesRequerentesPSS;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes;
using TRF3.SISPREC.SentencaTipos;
using TRF3.SISPREC.ServidorCondicaoTipos;
using TRF3.SISPREC.SincronizacoesDominios;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.TiposProcedimentos;
using TRF3.SISPREC.UnidadeJudicialTipoNaturezas;
using TRF3.SISPREC.UnidadeJudicialTipos;
using TRF3.SISPREC.Unidades;
using TRF3.SISPREC.UnidadesGestoras;
using TRF3.SISPREC.UnidadesJudiciais;
using TRF3.SISPREC.UnidadesOrcamentarias;
using TRF3.SISPREC.ValorTipos;
using TRF3.SISPREC.VerificacoesCnpjCpf;
using TRF3.SISPREC.ViewAuditoriaEntidades;
using TRF3.SISPREC.ViewAuditoriaPropriedades;
using TRF3.SISPREC.ViewControles;
using TRF3.SISPREC.ViewFases;
using TRF3.SISPREC.ViewProcessos;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace TRF3.SISPREC.EntityFrameworkCore;

[DependsOn(
    typeof(ProcessManagementEntityFrameworkCoreModule),
    typeof(SISPRECDomainModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpAuditLoggingEntityFrameworkCoreModule),
    typeof(AbpSettingManagementEntityFrameworkCoreModule),
    typeof(AbpTenantManagementEntityFrameworkCoreModule)
    )]
[ExcludeFromCodeCoverage]
public class SISPRECEntityFrameworkCoreModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        SISPRECEfCoreEntityExtensionMappings.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<SISPRECDbContext>(options =>
        {
            /* Remove "includeAllEntities: true" to create
             * default repositories only for aggregate roots */
            options.AddDefaultRepositories(includeAllEntities: true);
            options.AddRepository<Assunto, AssuntoRepository>();
            options.AddRepository<AntecessorBeneficiario, AntecessorBeneficiarioRepository>();
            options.AddRepository<AcaoOriginaria, AcaoOriginariaRepository>();
            options.AddRepository<IndiceAtualizacaoMonetariaTipo, IndiceAtualizacaoMonetariaTipoRepository>();
            options.AddRepository<LogGeral, LogGeralRepository>();
            options.AddRepository<LogDetalhe, LogDetalheRepository>();
            options.AddRepository<Beneficiario, BeneficiarioRepository>();
            options.AddRepository<IndiceAtualizacaoMonetaria, IndiceAtualizacaoMonetariaRepository>();
            options.AddRepository<UnidadeGestora, UnidadeGestoraRepository>();
            options.AddRepository<OrdemPagamento107aTipo, OrdemPagamento107aTipoRepository>();
            options.AddRepository<Parcela, ParcelaRepository>();
            options.AddRepository<Parte, ParteRepository>();
            options.AddRepository<ControleProcessamentoProcesso, ControleProcessamentoProcessoRepository>();
            options.AddRepository<ContaBancaria, ContaBancariaRepository>();
            options.AddRepository<MovimentoTipo, MovimentoTipoRepository>();
            options.AddRepository<SentencaTipo, SentencaTipoRepository>();
            options.AddRepository<ControleProcessamentoArquivo, ControleProcessamentoArquivoRepository>();
            options.AddRepository<BeneficiarioSucessaoTipo, BeneficiarioSucessaoTipoRepository>();
            options.AddRepository<DespesaTipo, DespesaTipoRepository>();
            options.AddRepository<ProcessoAnterior, ProcessoAnteriorRepository>();
            options.AddRepository<Processo, ProcessoRepository>();
            options.AddRepository<Fase, FaseRepository>();
            options.AddRepository<UnidadeJudicial, UnidadeJudicialRepository>();
            options.AddRepository<UnidadeOrcamentaria, UnidadeOrcamentariaRepository>();
            options.AddRepository<UnidadeJudicialTipo, UnidadeJudicialTipoRepository>();
            options.AddRepository<ControleProcessamento, ControleProcessamentoRepository>();
            options.AddRepository<ContaBancariaBeneficiario, ContaBancariaBeneficiarioRepository>();
            options.AddRepository<DespesaNatureza, DespesaNaturezaRepository>();
            options.AddRepository<Plano, PlanoRepository>();
            options.AddRepository<BeneficiarioOrigemPCT, BeneficiarioOrigemPCTRepository>();
            options.AddRepository<BeneficiarioOrigemRPV, BeneficiarioOrigemRPVRepository>();
            options.AddRepository<SincronizacaoDominio, SincronizacaoDominioRepository>();
            options.AddRepository<ValorTipo, ValorTipoRepository>();
            options.AddRepository<DespesaClassificacao, DespesaClassificacaoRepository>();
            options.AddRepository<BeneficiarioTipo, BeneficiarioTipoRepository>();
            options.AddRepository<BeneficiarioIdentificacaoTipo, BeneficiarioIdentificacaoTipoRepository>();
            options.AddRepository<FaseTipo, FaseTipoRepository>();
            options.AddRepository<ViewFase, ViewFaseRepository>();
            options.AddRepository<ViewControle, ViewControleRepository>();
            options.AddRepository<ViewProcesso, ViewProcessoRepository>();
            options.AddRepository<UnidadeJudicialTipoNatureza, UnidadeJudicialTipoNaturezaRepository>();
            options.AddRepository<AcaoJustificativa, AcaoJustificativaRepository>();
            options.AddRepository<ViewAuditoriaEntidade, ViewAuditoriaEntidadeRepository>();
            options.AddRepository<ViewAuditoriaPropriedade, ViewAuditoriaPropriedadeRepository>();
            options.AddRepository<ServidorCondicaoTipo, ServidorCondicaoTipoRepository>();
            options.AddRepository<AssuntoAuxiliar, AssuntoAuxiliarRepository>();
            options.AddRepository<AssuntoDespesa, AssuntoDespesaRepository>();
            options.AddRepository<Unidade, UnidadeRepository>();

            options.AddRepository<RequisicaoProtocolo, RequisicaoProtocoloRepository>();
            options.AddRepository<Proposta, PropostaRepository>();
            options.AddRepository<RequisicaoProposta, RequisicaoPropostaRepository>();
            options.AddRepository<SituacaoRequisicaoProtocolo, SituacaoRequisicaoProtocoloRepository>();
            options.AddRepository<RequisicaoProcessoOrigem, RequisicaoProcessoOrigemRepository>();
            options.AddRepository<Pessoa, PessoaRepository>();
            options.AddRepository<EnderecoPessoa, EnderecoPessoaRepository>();
            options.AddRepository<AssuntoExecucao, AssuntoExecucaoRepository>();
            options.AddRepository<AdvogadoJudicial, AdvogadoJudicialRepository>();
            options.AddRepository<TipoProcedimento, TipoProcedimentoRepository>();
            options.AddRepository<Banco, BancoRepository>();
            options.AddRepository<Agencia, AgenciaRepository>();
            options.AddRepository<OcorrenciaMotivo, OcorrenciaMotivoRepository>();
            options.AddRepository<RequisicaoOcorrencia, RequisicaoOcorrenciaRepository>();
            options.AddRepository<RequisicaoParteRequerenteIr, RequisicaoParteRequerenteIrRepository>();
            options.AddRepository<RequisicaoParteRequerentePss, RequisicaoParteRequerentePssRepository>();

            options.AddRepository<RequisicaoParte, RequisicaoParteRepository>();
            options.AddRepository<VerificacaoCnpjCpf, VerificacaoCnpjCpfRepository>();
            options.AddRepository<AcaoTipo, AcaoTipoRepository>();
            options.AddRepository<CodigoReceitaFederal, CodigoReceitaFederalRepository>();
            options.AddRepository<RequisicaoRequerenteCompensado, RequisicaoParteRequerenteCompensadoRepository>();
            options.AddRepository<ControleImportacaoRequisicao, ControleImportacaoRequisicaoRepository>();
            options.AddRepository<Municipio, MunicipioRepository>();
            options.AddRepository<CidadeRegPag, CidadeReqPagRepository>();
            options.AddRepository<SinPessoaReqPag, SinPessoaReqPagRepository>();
            options.AddRepository<ModeloDocumento, ModeloDocumentoRepository>();
            options.AddRepository<RequisicaoPlanoOrcamento, RequisicaoPlanoOrcamentoRepository>();
            options.AddRepository<RequisicaoEstorno, RequisicaoEstornoRepository>();
            options.AddRepository<IndicadorEconomicoTipo, IndicadorEconomicoTipoRepository>();
            options.AddRepository<IndicadorEconomico, IndicadorEconomicoRepository>();
            options.AddRepository<Perito, PeritoRepository>();
            options.AddRepository<RequisicaoJustificativa, RequisicaoJustificativaRepository>();
            options.AddRepository<JustificativaComparacao, JustificativaComparacaoRepository>();
            options.AddRepository<JustificativaDocumento, JustificativaDocumentoRepository>();
        });

        Configure<AbpDbContextOptions>(options =>
        {
            /* The main point to change your DBMS.
             * See also SISPRECMigrationsDbContextFactory for EF Core tooling. */
            options.UseSqlServer();
        });

    }
}
