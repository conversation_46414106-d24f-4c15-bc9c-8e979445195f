using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Localization;
using EasyAbp.ProcessManagement;
using Volo.Abp.AspNetCore.SignalR;
using Volo.Abp.Localization;
using Volo.Abp.Localization.ExceptionHandling;
using Volo.Abp.Modularity;
using Volo.Abp.SettingManagement;
using Volo.Abp.Validation.Localization;
using Volo.Abp.VirtualFileSystem;

namespace TRF3.SISPREC;


[ExcludeFromCodeCoverage]
[DependsOn(
    typeof(ProcessManagementDomainSharedModule),
    typeof(AbpSettingManagementDomainSharedModule),
    typeof(AbpAspNetCoreSignalRModule)
    )]
public class SISPRECDomainSharedModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        GlobalFeatureConfigurator.Configure();
        ModuleExtensionConfigurator.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<SISPRECDomainSharedModule>();
        });

        Configure<AbpLocalizationOptions>(options =>
        {
            _ = options.Resources
                .Add<SISPRECResource>("pt-BR")
                .AddBaseTypes(typeof(AbpValidationResource))
                .AddVirtualJson("/Localization/SISPREC");

            options.DefaultResourceType = typeof(SISPRECResource);
        });

        Configure<AbpExceptionLocalizationOptions>(options =>
        {
            options.MapCodeNamespace("SISPREC", typeof(SISPRECResource));
        });
    }
}
